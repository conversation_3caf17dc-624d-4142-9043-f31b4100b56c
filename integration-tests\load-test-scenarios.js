#!/usr/bin/env node

/**
 * ATMA Load Test Scenarios
 * Berbagai skenario load testing untuk kebutuhan yang berbeda
 */

const LoadTestRunner = require('./load-test');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Predefined test scenarios
const scenarios = {
  'smoke': {
    name: 'Smoke Test',
    description: 'Quick test with minimal load',
    totalUsers: 10,
    concurrency: 5,
    duration: '~2 minutes'
  },
  'light': {
    name: 'Light Load Test', 
    description: 'Light load for basic performance check',
    totalUsers: 50,
    concurrency: 10,
    duration: '~5 minutes'
  },
  'medium': {
    name: 'Medium Load Test',
    description: 'Medium load for regular performance testing',
    totalUsers: 200,
    concurrency: 25,
    duration: '~10 minutes'
  },
  'heavy': {
    name: 'Heavy Load Test',
    description: 'Heavy load for stress testing',
    totalUsers: 500,
    concurrency: 50,
    duration: '~15 minutes'
  },
  'stress': {
    name: 'Stress Test',
    description: 'Maximum load to find breaking point',
    totalUsers: 1000,
    concurrency: 100,
    duration: '~25 minutes'
  },
  'spike': {
    name: 'Spike Test',
    description: 'High concurrency with fewer total users',
    totalUsers: 100,
    concurrency: 50,
    duration: '~5 minutes'
  },
  'endurance': {
    name: 'Endurance Test',
    description: 'Sustained load over longer period',
    totalUsers: 300,
    concurrency: 20,
    duration: '~20 minutes'
  }
};

function printScenarios() {
  console.log(colorize('\n🎯 Available Load Test Scenarios', 'cyan'));
  console.log('='.repeat(80));
  
  Object.entries(scenarios).forEach(([key, scenario]) => {
    console.log(colorize(`\n${key}`, 'bright') + colorize(` - ${scenario.name}`, 'green'));
    console.log(`   ${scenario.description}`);
    console.log(`   👥 Users: ${scenario.totalUsers}, ⚡ Concurrency: ${scenario.concurrency}`);
    console.log(`   ⏱️  Estimated Duration: ${scenario.duration}`);
  });
  
  console.log('\n' + '='.repeat(80));
  console.log(colorize('Usage:', 'yellow'));
  console.log('  node load-test-scenarios.js [scenario]');
  console.log('  node load-test-scenarios.js custom [totalUsers] [concurrency]');
  console.log('\nExamples:');
  console.log('  node load-test-scenarios.js heavy');
  console.log('  node load-test-scenarios.js custom 750 75');
}

async function runScenario(scenarioKey) {
  const scenario = scenarios[scenarioKey];
  
  if (!scenario) {
    console.log(colorize(`❌ Unknown scenario: ${scenarioKey}`, 'red'));
    printScenarios();
    return;
  }
  
  console.log(colorize(`\n🎯 Running ${scenario.name}`, 'cyan'));
  console.log('='.repeat(80));
  console.log(colorize(`📝 Description: ${scenario.description}`, 'yellow'));
  console.log(colorize(`👥 Total Users: ${scenario.totalUsers}`, 'yellow'));
  console.log(colorize(`⚡ Concurrency: ${scenario.concurrency}`, 'yellow'));
  console.log(colorize(`⏱️  Estimated Duration: ${scenario.duration}`, 'yellow'));
  console.log('='.repeat(80));
  
  // Confirmation prompt for heavy scenarios
  if (scenario.totalUsers >= 500) {
    console.log(colorize('\n⚠️  WARNING: This is a heavy load test!', 'yellow'));
    console.log('Make sure:');
    console.log('- All services are running and healthy');
    console.log('- Database can handle the load');
    console.log('- You have sufficient system resources');
    console.log('- This is NOT running against production');
    
    // In a real scenario, you might want to add a confirmation prompt here
    console.log(colorize('\nProceeding in 5 seconds... (Ctrl+C to cancel)', 'yellow'));
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  const loadTest = new LoadTestRunner();
  await loadTest.runLoadTest(scenario.totalUsers, scenario.concurrency);
}

async function runCustomScenario(totalUsers, concurrency) {
  console.log(colorize('\n🎯 Running Custom Load Test', 'cyan'));
  console.log('='.repeat(80));
  console.log(colorize(`👥 Total Users: ${totalUsers}`, 'yellow'));
  console.log(colorize(`⚡ Concurrency: ${concurrency}`, 'yellow'));
  console.log('='.repeat(80));
  
  const loadTest = new LoadTestRunner();
  await loadTest.runLoadTest(totalUsers, concurrency);
}

async function runProgressiveTest() {
  console.log(colorize('\n🎯 Running Progressive Load Test', 'cyan'));
  console.log('This will run multiple scenarios in sequence to find the breaking point');
  console.log('='.repeat(80));
  
  const progressiveScenarios = ['smoke', 'light', 'medium', 'heavy'];
  
  for (const scenarioKey of progressiveScenarios) {
    const scenario = scenarios[scenarioKey];
    console.log(colorize(`\n🔄 Starting ${scenario.name}...`, 'blue'));
    
    try {
      const loadTest = new LoadTestRunner();
      await loadTest.runLoadTest(scenario.totalUsers, scenario.concurrency);
      
      // Check if success rate is acceptable to continue
      if (loadTest.results.successful / loadTest.results.total < 0.8) {
        console.log(colorize(`\n⚠️  Success rate below 80%, stopping progressive test`, 'yellow'));
        break;
      }
      
      console.log(colorize(`✅ ${scenario.name} completed successfully`, 'green'));
      
      // Wait between scenarios
      if (scenarioKey !== progressiveScenarios[progressiveScenarios.length - 1]) {
        console.log(colorize('\n⏳ Waiting 30 seconds before next scenario...', 'blue'));
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
      
    } catch (error) {
      console.log(colorize(`❌ ${scenario.name} failed: ${error.message}`, 'red'));
      console.log(colorize('Stopping progressive test', 'yellow'));
      break;
    }
  }
  
  console.log(colorize('\n🎉 Progressive load test completed!', 'green'));
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || command === 'help') {
    printScenarios();
    return;
  }
  
  try {
    if (command === 'custom') {
      const totalUsers = parseInt(args[1]) || 100;
      const concurrency = parseInt(args[2]) || 20;
      await runCustomScenario(totalUsers, concurrency);
    } else if (command === 'progressive') {
      await runProgressiveTest();
    } else if (scenarios[command]) {
      await runScenario(command);
    } else {
      console.log(colorize(`❌ Unknown command: ${command}`, 'red'));
      printScenarios();
      process.exit(1);
    }
    
    console.log(colorize('\n🎉 Load test scenario completed successfully!', 'green'));
    
  } catch (error) {
    console.log(colorize(`\n💥 Load test scenario failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️ Load test scenario interrupted by user', 'yellow'));
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = {
  scenarios,
  runScenario,
  runCustomScenario,
  runProgressiveTest
};
