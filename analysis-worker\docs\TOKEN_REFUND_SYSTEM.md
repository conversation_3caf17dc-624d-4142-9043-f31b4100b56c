# Token Refund System untuk AI Service Failures

## Overview

Sistem ini menangani kegagalan AI service dengan cara:
1. **Mengembalikan token user** yang sudah terpotong
2. **Menyimpan hasil kosong** ke database dengan status "failed"
3. **Mencegah retry yang tidak perlu** untuk menghemat token
4. **Memungkinkan user untuk retry** ketika service sudah tersedia

## Flow Diagram

```
User Submit Assessment
        ↓
Token Deducted (Assessment Service)
        ↓
Job Queued (RabbitMQ)
        ↓
Analysis Worker Process
        ↓
    AI Service Call
        ↓
   [SUCCESS] ────────────→ Save Result → Update Job Status → Complete
        ↓
   [FAILURE]
        ↓
Save Failed Result (Archive Service)
        ↓
Update Job Status to "failed" (Assessment Service)
        ↓
Refund Tokens (Auth Service)
        ↓
Send Failure Notification
        ↓
No Retry (Prevent Token Waste)
```

## Komponen yang Dimodifikasi

### 1. Assessment Service
- **File**: `src/services/authService.js`
- **Fungsi Baru**: `refundTokens(userId, token, tokenAmount)`
- **Endpoint Baru**: `POST /assessments/callback/failed`

### 2. Analysis Worker
- **File**: `src/processors/assessmentProcessor.js`
- **Perubahan**: Menangani AI failure dengan menyimpan hasil kosong dan refund token
- **File**: `src/services/queueConsumer.js`
- **Perubahan**: Mencegah retry untuk AI service errors

### 3. Archive Service
- **File**: `src/models/AnalysisResult.js`
- **Perubahan**: 
  - `persona_profile` sekarang nullable
  - Menambahkan field `error_message`
- **File**: `src/services/archiveService.js`
- **Fungsi Baru**: `saveFailedAnalysisResult()`

### 4. Notification Service
- **File**: `src/services/notificationService.js`
- **Fungsi Baru**: `updateAssessmentJobStatusFailed()`

## Konfigurasi Environment

```env
# Analysis Worker
MAX_RETRIES=3
RETRY_DELAY=5000
ANALYSIS_TOKEN_COST=1

# Assessment Service
ANALYSIS_TOKEN_COST=1
AUTH_SERVICE_KEY=your_internal_service_key
```

## Error Handling Strategy

### AI Service Errors (No Retry)
- Google AI API errors
- Invalid persona profile
- AI service timeout
- **Action**: Refund token, save failed result, no retry

### Infrastructure Errors (Retry)
- Database connection errors
- Archive service unavailable
- Network timeouts
- **Action**: Retry with exponential backoff

### Validation Errors (No Retry)
- Invalid assessment data
- Invalid job format
- **Action**: Send to DLQ immediately

## Database Schema Changes

### Archive Service Migration
```sql
-- Add error_message column
ALTER TABLE archive.analysis_results 
ADD COLUMN error_message TEXT;

-- Make persona_profile nullable
ALTER TABLE archive.analysis_results 
ALTER COLUMN persona_profile DROP NOT NULL;
```

## API Endpoints

### Assessment Service
```http
POST /assessments/callback/failed
Content-Type: application/json
X-Service-Key: internal_service_key

{
  "jobId": "uuid",
  "resultId": "uuid",
  "status": "failed",
  "errorMessage": "AI service error message"
}
```

### Auth Service (Internal)
```http
PUT /auth/token-balance
Content-Type: application/json
X-Internal-Service: true
X-Service-Key: internal_service_key

{
  "userId": "uuid",
  "amount": 1,
  "operation": "add"
}
```

## Monitoring & Logging

### Key Log Events
1. **Token Deduction**: `Token deduction successful`
2. **AI Failure**: `Assessment processing failed`
3. **Failed Result Saved**: `Failed analysis result saved to Archive Service`
4. **Token Refund**: `Token refund successful`
5. **No Retry**: `Error should not be retried (AI service error)`

### Metrics to Monitor
- Token refund rate
- AI service failure rate
- Failed analysis results count
- Queue retry patterns

## User Experience

### Before (Masalah)
1. User submit assessment → Token terpotong
2. AI service gagal → Token hilang, tidak ada hasil
3. User harus contact support untuk refund

### After (Solusi)
1. User submit assessment → Token terpotong
2. AI service gagal → Token dikembalikan otomatis
3. Hasil kosong disimpan dengan status "failed"
4. User bisa retry kapan saja tanpa kehilangan token

## Testing

### Unit Tests
```bash
# Test token refund functionality
npm test -- --grep "refund tokens"

# Test failed result saving
npm test -- --grep "save failed analysis"
```

### Integration Tests
```bash
# Test end-to-end failure scenario
npm run test:integration -- --grep "AI service failure"
```

## Rollback Plan

Jika ada masalah dengan sistem baru:

1. **Disable token refund**: Set `ENABLE_TOKEN_REFUND=false`
2. **Revert queue behavior**: Set `RETRY_AI_ERRORS=true`
3. **Database rollback**: Run migration down
4. **Monitor logs**: Check for any issues

## Future Improvements

1. **Partial Refund**: Refund berdasarkan tingkat kegagalan
2. **Retry Limits**: Batas retry per user per hari
3. **Credit System**: Sistem kredit yang lebih sophisticated
4. **Analytics**: Dashboard untuk monitoring failure patterns
