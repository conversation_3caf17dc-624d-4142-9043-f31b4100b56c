const ApiClient = require('./api-client');
const fs = require('fs');
const path = require('path');

class TestHelpers {
  constructor(silent = false) {
    this.authClient = new ApiClient(global.testConfig.authServiceUrl, {}, silent);
    this.assessmentClient = new ApiClient(global.testConfig.assessmentServiceUrl, {}, silent);
    this.archiveClient = new ApiClient(global.testConfig.archiveServiceUrl, {}, silent);
    this.apiGatewayClient = new ApiClient(global.testConfig.apiGatewayUrl, {}, silent);
    this.notificationClient = new ApiClient(global.testConfig.notificationServiceUrl || 'http://localhost:3005', {}, silent);
  }

  /**
   * Register a new user
   */
  async registerUser(email = null, password = null) {
    const testEmail = email || global.generateTestEmail();
    const testPassword = password || global.testConfig.testPassword;

    const response = await this.authClient.post('/auth/register', {
      email: testEmail,
      password: testPassword
    });

    return {
      email: testEmail,
      password: testPassword,
      response
    };
  }

  /**
   * <PERSON>gin user and get JWT token
   */
  async loginUser(email, password) {
    const response = await this.authClient.post('/auth/login', {
      email,
      password
    });

    const token = response.data.token;

    return {
      token,
      user: response.data.user,
      response
    };
  }

  /**
   * Submit assessment data
   */
  async submitAssessment(token, assessmentData = null) {
    if (!assessmentData) {
      assessmentData = this.loadSampleAssessment();
    }

    // Set auth token
    this.assessmentClient.setAuthToken(token);

    const response = await this.assessmentClient.post('/assessments/submit', assessmentData);

    return response;
  }

  /**
   * Check assessment status
   */
  async checkAssessmentStatus(token, jobId) {
    this.assessmentClient.setAuthToken(token);
    const response = await this.assessmentClient.get(`/assessments/status/${jobId}`);
    return response;
  }

  /**
   * Get analysis results from archive service
   */
  async getAnalysisResults(token, userId = null) {
    this.archiveClient.setAuthToken(token);

    let url = '/archive/results';
    if (userId) {
      url += `?user_id=${userId}`;
    }

    const response = await this.archiveClient.get(url);
    return response;
  }

  /**
   * Wait for assessment processing to complete
   */
  async waitForAssessmentCompletion(token, jobId, maxWaitTime = 120000) {
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds
    let lastStatus = 'unknown';

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const statusResponse = await this.checkAssessmentStatus(token, jobId);
        const status = statusResponse.data.status;
        lastStatus = status;

        if (status === 'completed') {
          return statusResponse;
        } else if (status === 'failed') {
          throw new Error(`Assessment processing failed: ${statusResponse.data.error || 'Unknown error'}`);
        }

        await global.sleep(pollInterval);

      } catch (error) {
        if (error.response && error.response.status === 404) {
          lastStatus = 'not_found';
          await global.sleep(pollInterval);
          continue;
        }
        throw error;
      }
    }

    throw new Error(`Assessment processing timeout after ${maxWaitTime}ms. Last status: ${lastStatus}`);
  }

  /**
   * Load sample assessment data
   */
  loadSampleAssessment() {
    const filePath = path.join(__dirname, '..', 'test-data', 'sample-assessment.json');
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  }

  /**
   * Verify assessment result structure
   */
  verifyAssessmentResult(result) {
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('user_id');
    expect(result).toHaveProperty('assessment_data');
    expect(result).toHaveProperty('persona_profile');
    expect(result).toHaveProperty('status', 'completed');
    expect(result).toHaveProperty('created_at');
    expect(result).toHaveProperty('updated_at');

    // Verify persona profile structure
    const personaProfile = result.persona_profile;
    expect(personaProfile).toHaveProperty('archetype');
    expect(personaProfile).toHaveProperty('description');
    expect(personaProfile).toHaveProperty('strengths');
    expect(personaProfile).toHaveProperty('challenges');
    expect(personaProfile).toHaveProperty('careerSuggestions');
    expect(personaProfile).toHaveProperty('developmentAreas');

    console.log(`✅ Assessment result structure verified`);
    console.log(`🎭 Persona archetype: ${personaProfile.archetype}`);
  }

  /**
   * Check notification service status
   */
  async checkNotificationService() {
    try {
      console.log(`🔔 Checking notification service health`);

      // Set service auth for notification service
      this.notificationClient.setServiceAuth(global.testConfig.serviceApiKey);

      const response = await this.notificationClient.get('/notifications/status');

      console.log(`✅ Notification service is operational`);
      console.log(`📊 Active connections: ${response.data.connections.total}`);

      return response;
    } catch (error) {
      console.log(`❌ Notification service check failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check all services health
   */
  async checkAllServicesHealth() {
    console.log(`🏥 Checking all services health`);

    const services = [
      { name: 'Auth Service', client: this.authClient, endpoint: '/health' },
      { name: 'Assessment Service', client: this.assessmentClient, endpoint: '/health/live' },
      { name: 'Archive Service', client: this.archiveClient, endpoint: '/health' },
      { name: 'Notification Service', client: this.notificationClient, endpoint: '/health' }
    ];

    const results = {};

    for (const service of services) {
      try {
        console.log(`🔍 Checking ${service.name}...`);

        if (service.name === 'Notification Service') {
          service.client.setServiceAuth(global.testConfig.serviceApiKey);
        }

        const response = await service.client.get(service.endpoint);
        results[service.name] = {
          status: 'healthy',
          data: response
        };
        console.log(`✅ ${service.name}: Healthy`);
      } catch (error) {
        results[service.name] = {
          status: 'unhealthy',
          error: error.message
        };
        console.log(`❌ ${service.name}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Wait for notification (if WebSocket is available)
   */
  async waitForNotification(userId, jobId, maxWaitTime = 30000) {
    console.log(`🔔 Waiting for notification for job: ${jobId}`);

    // This is a placeholder for WebSocket notification monitoring
    // In a real implementation, you would connect to the WebSocket
    // and listen for the 'analysis-complete' event

    console.log(`⚠️ WebSocket notification monitoring not implemented in tests`);
    console.log(`💡 You can manually check notifications at ws://localhost:3005`);

    return { received: false, reason: 'WebSocket monitoring not implemented' };
  }

  /**
   * Clean up test data (if needed)
   */
  async cleanup(userId) {
    console.log(`🧹 Cleaning up test data for user: ${userId}`);
    // Implementation depends on available cleanup endpoints
    // This is a placeholder for future cleanup functionality
  }
}

module.exports = TestHelpers;
