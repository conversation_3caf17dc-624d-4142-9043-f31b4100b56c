{"name": "atma-integration-tests", "version": "1.0.0", "description": "Integration tests for ATMA Backend services", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:full-flow": "node full-flow-test.js", "test:individual": "jest individual-tests.test.js --verbose", "test:multiple": "jest multiple-assessments.test.js --verbose", "test:load": "node load-test.js", "test:load-heavy": "node load-test-scenarios.js heavy", "test:load-medium": "node load-test-scenarios.js medium", "test:load-light": "node load-test-scenarios.js light", "test:load-progressive": "node load-test-scenarios.js progressive", "test:health": "node run-tests.js health", "test:all": "npm run test:health && npm run test:full-flow && npm run test:individual", "debug:auth": "node debug-service-auth.js", "runner": "node run-tests.js"}, "keywords": ["atma", "integration", "testing", "assessment"], "author": "ATMA Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "p-limit": "^3.1.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}}