# ATMA Load Test Guide - 500 Concurrent Users

Panduan lengkap untuk melakukan load testing dengan 500 user bersamaan pada sistem ATMA Backend.

## 📋 Daftar Isi

1. [Persiapan](#persiapan)
2. [Menjalankan Load Test](#menjalankan-load-test)
3. [Konfigurasi](#konfigurasi)
4. [Interpretasi Hasil](#interpretasi-hasil)
5. [Troubleshooting](#troubleshooting)

## 🚀 Persiapan

### 1. Pastikan Semua Service Berjalan

Sebelum menjalankan load test, pastikan semua service ATMA Backend sudah berjalan:

```bash
# Cek status service
npm run test:health
```

Service yang harus berjalan:
- **Auth Service** (port 3001)
- **Assessment Service** (port 3003) 
- **Archive Service** (port 3002)
- **Notification Service** (port 3005)

### 2. Konfigurasi Environment

Pastikan file `.env` sudah dikonfigurasi dengan benar:

```env
AUTH_SERVICE_URL=http://localhost:3001
ASSESSMENT_SERVICE_URL=http://localhost:3003
ARCHIVE_SERVICE_URL=http://localhost:3002
NOTIFICATION_SERVICE_URL=http://localhost:3005
API_GATEWAY_URL=http://localhost:3000
TEST_PASSWORD=testpass123
SERVICE_API_KEY=test-service-key
```

### 3. Install Dependencies

```bash
npm install
```

## ⚡ Menjalankan Load Test

### Metode 1: Menggunakan Test Runner

```bash
# Menjalankan load test dengan 500 user (default)
node run-tests.js load-test
```

### Metode 2: Menjalankan Langsung

```bash
# Load test dengan 500 user, 50 concurrent
node load-test.js

# Load test dengan custom parameter
node load-test.js [totalUsers] [concurrency]

# Contoh: 100 user dengan 20 concurrent
node load-test.js 100 20

# Contoh: 1000 user dengan 100 concurrent  
node load-test.js 1000 100
```

### Metode 3: Menggunakan NPM Script

```bash
# Menjalankan load test
npm run test:load
```

## ⚙️ Konfigurasi

### Parameter Load Test

| Parameter | Default | Deskripsi |
|-----------|---------|-----------|
| `totalUsers` | 500 | Total jumlah user yang akan ditest |
| `concurrency` | 50 | Jumlah user yang berjalan bersamaan |

### Skenario Test per User

Setiap user akan menjalankan skenario lengkap:

1. **Register** - Membuat akun baru
2. **Login** - Masuk ke sistem  
3. **Submit Assessment** - Mengirim data assessment
4. **Wait for Processing** - Menunggu pemrosesan (timeout 60s)
5. **Verify Results** - Memverifikasi hasil di archive

### Timeout Settings

- **Assessment Processing**: 60 detik per user
- **API Request**: 30 detik per request
- **Archive Verification**: 2 detik delay

## 📊 Interpretasi Hasil

### Output Real-time

Selama test berjalan, Anda akan melihat:

```
🚀 Starting 500 user tests with 50 concurrent...
⏳ Progress: 45/500 (9%) - Success: 42, Failed: 3
⏳ Progress: 98/500 (19%) - Success: 94, Failed: 4
...
```

### Laporan Akhir

Setelah test selesai, akan ditampilkan laporan lengkap:

```
📊 LOAD TEST RESULTS
================================================================================
📈 Summary:
   Total Users: 500
   Successful: 485
   Failed: 15  
   Success Rate: 97%

⏱️  Performance:
   Total Duration: 245s
   Average Response Time: 28500ms
   Min Response Time: 15200ms
   Max Response Time: 58900ms
   Throughput: 1.98 requests/second

🎯 Performance Analysis:
   ✅ Excellent: 97% success rate
   ✅ Good response time: 28500ms average
   ⚠️  Moderate throughput: 1.98 req/s
```

### Kriteria Penilaian

#### Success Rate
- **✅ Excellent**: ≥95% success rate
- **⚠️ Good**: 80-94% success rate  
- **❌ Poor**: <80% success rate

#### Response Time
- **✅ Good**: ≤30 detik average
- **⚠️ Acceptable**: 30-60 detik average
- **❌ Slow**: >60 detik average

#### Throughput
- **✅ Good**: ≥5 requests/second
- **⚠️ Moderate**: 2-4.9 requests/second
- **❌ Low**: <2 requests/second

### File Hasil

Hasil detail disimpan dalam format JSON di folder `test-results/`:

```
test-results/
├── load-test-results-2025-07-19T10-30-45-123Z.json
└── load-test-results-2025-07-19T11-15-22-456Z.json
```

## 🔧 Troubleshooting

### Error: Service Not Running

```
❌ Auth Service: Service not running
```

**Solusi**: Pastikan service berjalan di port yang benar:
```bash
# Cek port yang digunakan
netstat -an | findstr :3001
netstat -an | findstr :3003
netstat -an | findstr :3002
netstat -an | findstr :3005
```

### Error: High Failure Rate

Jika success rate <80%:

1. **Kurangi concurrency**:
   ```bash
   node load-test.js 500 25  # Turunkan dari 50 ke 25
   ```

2. **Cek resource sistem**:
   - CPU usage
   - Memory usage  
   - Database connections

3. **Increase timeout**:
   Edit `load-test.js` dan ubah timeout dari 60000 ke 90000ms

### Error: Slow Response Time

Jika response time >60 detik:

1. **Optimize database**:
   - Check database performance
   - Optimize queries
   - Add indexes if needed

2. **Scale services**:
   - Increase service instances
   - Use load balancer

3. **Tune concurrency**:
   ```bash
   node load-test.js 500 20  # Kurangi concurrency
   ```

### Error: Memory Issues

```
FATAL ERROR: Ineffective mark-compacts near heap limit
```

**Solusi**: Increase Node.js memory:
```bash
node --max-old-space-size=4096 load-test.js
```

### Error: Database Connection Limit

```
Error: Too many connections
```

**Solusi**:
1. Increase database max connections
2. Implement connection pooling
3. Reduce concurrency level

## 📈 Optimasi Performance

### 1. Database Tuning

```sql
-- Increase connection limit
SET GLOBAL max_connections = 1000;

-- Optimize buffer pool
SET GLOBAL innodb_buffer_pool_size = 2G;
```

### 2. Service Configuration

Increase worker processes di setiap service:

```javascript
// Contoh untuk Express.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
} else {
  // Start server
}
```

### 3. Load Balancer

Gunakan nginx untuk load balancing:

```nginx
upstream backend {
    server localhost:3001;
    server localhost:3011;
    server localhost:3021;
}

server {
    listen 80;
    location / {
        proxy_pass http://backend;
    }
}
```

## 🎯 Best Practices

1. **Mulai dengan test kecil**: Test dengan 50-100 user dulu
2. **Monitor resource**: Pantau CPU, memory, dan database
3. **Gradual increase**: Naikkan load secara bertahap
4. **Test di environment terpisah**: Jangan test di production
5. **Backup data**: Backup database sebelum load test
6. **Clean up**: Hapus test data setelah selesai

## 📞 Support

Jika mengalami masalah:

1. Cek log service untuk error details
2. Monitor system resources
3. Review database performance
4. Konsultasi dengan tim DevOps

---

**Happy Load Testing! 🚀**
