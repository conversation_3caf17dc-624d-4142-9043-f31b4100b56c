@echo off
REM ATMA Load Test Runner for Windows
REM Batch script untuk men<PERSON>lankan berb<PERSON>i skenario load test

echo.
echo ===============================================================================
echo                        ATMA LOAD TEST RUNNER
echo ===============================================================================
echo.

if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    exit /b 1
)

REM Check if npm dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        exit /b 1
    )
)

echo Checking service health...
node run-tests.js health
if errorlevel 1 (
    echo.
    echo WARNING: Some services are not healthy
    echo Continue anyway? (Y/N)
    set /p continue=
    if /i not "%continue%"=="Y" (
        echo Load test cancelled
        exit /b 1
    )
)

echo.
echo Starting load test...
echo.

if "%1"=="500" goto run_500_users
if "%1"=="heavy" goto run_heavy
if "%1"=="medium" goto run_medium
if "%1"=="light" goto run_light
if "%1"=="smoke" goto run_smoke
if "%1"=="stress" goto run_stress
if "%1"=="progressive" goto run_progressive
if "%1"=="custom" goto run_custom

REM Default: run 500 users
:run_500_users
echo Running load test with 500 concurrent users...
node load-test.js 500 50
goto end

:run_heavy
echo Running heavy load test scenario...
node load-test-scenarios.js heavy
goto end

:run_medium
echo Running medium load test scenario...
node load-test-scenarios.js medium
goto end

:run_light
echo Running light load test scenario...
node load-test-scenarios.js light
goto end

:run_smoke
echo Running smoke test scenario...
node load-test-scenarios.js smoke
goto end

:run_stress
echo Running stress test scenario...
node load-test-scenarios.js stress
goto end

:run_progressive
echo Running progressive load test...
node load-test-scenarios.js progressive
goto end

:run_custom
if "%2"=="" (
    echo ERROR: Custom test requires total users parameter
    echo Usage: run-load-test.bat custom [totalUsers] [concurrency]
    exit /b 1
)
set total_users=%2
set concurrency=%3
if "%concurrency%"=="" set concurrency=20
echo Running custom load test with %total_users% users, %concurrency% concurrent...
node load-test-scenarios.js custom %total_users% %concurrency%
goto end

:show_help
echo Usage: run-load-test.bat [scenario]
echo.
echo Available scenarios:
echo   500          - Run 500 concurrent users (default)
echo   heavy        - Heavy load test (500 users, 50 concurrent)
echo   medium       - Medium load test (200 users, 25 concurrent)
echo   light        - Light load test (50 users, 10 concurrent)
echo   smoke        - Smoke test (10 users, 5 concurrent)
echo   stress       - Stress test (1000 users, 100 concurrent)
echo   progressive  - Progressive test (multiple scenarios)
echo   custom       - Custom test (requires parameters)
echo.
echo Examples:
echo   run-load-test.bat heavy
echo   run-load-test.bat custom 750 75
echo   run-load-test.bat progressive
echo.
goto end

:end
echo.
echo Load test completed!
echo Check the test-results folder for detailed reports.
pause
