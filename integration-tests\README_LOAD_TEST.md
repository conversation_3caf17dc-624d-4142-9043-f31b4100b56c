# Load Test untuk 500 User Bersamaan

## 🚀 <PERSON>

### 1. Test 500 User (Default)
```bash
node load-test.js
# atau
npm run test:load
```

### 2. Test dengan Skenario <PERSON>
```bash
# Smoke test (10 user)
node load-test-scenarios.js smoke

# Light test (50 user)  
node load-test-scenarios.js light

# Medium test (200 user)
node load-test-scenarios.js medium

# Heavy test (500 user)
node load-test-scenarios.js heavy

# Stress test (1000 user)
node load-test-scenarios.js stress
```

### 3. Custom Test
```bash
# Custom: 750 user dengan 75 concurrent
node load-test.js 750 75

# Custom dengan scenarios
node load-test-scenarios.js custom 750 75
```

### 4. Menggunakan Batch File (Windows)
```cmd
run-load-test.bat heavy
run-load-test.bat custom 750 75
```

## 📊 Output yang Dihasilkan

### Real-time Progress
```
user-1 register 1833ms
user-2 register 1833ms
user-1 login 68ms
user-2 login 195ms
user-1 submit 348ms
user-2 submit 227ms
user-1 process 35121ms
user-2 process 35116ms
user-1 SELESAI total 39394ms
user-2 SELESAI total 39390ms
```

### La<PERSON>an Akhir
```
📊 HASIL LOAD TEST
============================================================
Total Users: 500
Berhasil: 485 (97%)
Gagal: 15
Durasi Total: 245s
Rata-rata Full Journey: 28500ms per user
Throughput: 1.98 user/detik

📈 ANALISIS PER STEP:
register  : 97% berhasil, rata-rata 1850ms
login     : 98% berhasil, rata-rata 220ms
submit    : 99% berhasil, rata-rata 180ms
process   : 95% berhasil, rata-rata 25000ms
verify    : 97% berhasil, rata-rata 2000ms

🔍 ANALISIS BOTTLENECK:
🐌 process: 25000ms rata-rata
⚠️ verify: 2000ms rata-rata
📊 register: 1850ms rata-rata

🎯 KESIMPULAN:
✅ Sistem stabil untuk load ini
⚠️ Response time acceptable
============================================================
```

## 🔍 Interpretasi Hasil

### Success Rate
- **✅ Excellent**: ≥95% success rate
- **⚠️ Good**: 80-94% success rate  
- **❌ Poor**: <80% success rate

### Response Time
- **✅ Good**: ≤30 detik average
- **⚠️ Acceptable**: 30-60 detik average
- **❌ Slow**: >60 detik average

### Bottleneck Analysis
- **🐌**: Step paling lambat (bottleneck utama)
- **⚠️**: Step kedua terlambat
- **📊**: Step ketiga terlambat

## 📁 File Hasil

Hasil detail disimpan di folder `test-results/` dalam format JSON:
```
test-results/
├── load-test-results-2025-07-19T10-30-45-123Z.json
└── load-test-results-2025-07-19T11-15-22-456Z.json
```

## ⚙️ Konfigurasi

### Environment Variables (.env)
```env
AUTH_SERVICE_URL=http://localhost:3001
ASSESSMENT_SERVICE_URL=http://localhost:3003
ARCHIVE_SERVICE_URL=http://localhost:3002
NOTIFICATION_SERVICE_URL=http://localhost:3005
TEST_PASSWORD=testpass123
```

### Parameter Load Test
- `totalUsers`: Jumlah total user (default: 500)
- `concurrency`: Jumlah user bersamaan (default: 50)

## 🛠️ Troubleshooting

### Error: Service Not Running
```
❌ Auth Service: Request failed with status code 404
```
**Solusi**: Pastikan semua service berjalan di port yang benar

### High Failure Rate
Jika success rate <80%:
1. Kurangi concurrency: `node load-test.js 500 25`
2. Cek resource sistem (CPU, Memory)
3. Increase timeout di load-test.js

### Slow Response Time
Jika response time >60 detik:
1. Optimize database performance
2. Scale services (multiple instances)
3. Tune concurrency level

## 📈 Skenario Test yang Tersedia

| Skenario | Users | Concurrency | Durasi | Tujuan |
|----------|-------|-------------|--------|--------|
| smoke | 10 | 5 | ~2 min | Quick test |
| light | 50 | 10 | ~5 min | Basic performance |
| medium | 200 | 25 | ~10 min | Regular testing |
| heavy | 500 | 50 | ~15 min | Stress testing |
| stress | 1000 | 100 | ~25 min | Breaking point |

## 🎯 Best Practices

1. **Mulai kecil**: Test dengan smoke/light dulu
2. **Monitor resource**: Pantau CPU, memory, database
3. **Gradual increase**: Naikkan load secara bertahap
4. **Clean environment**: Test di environment terpisah
5. **Backup data**: Backup database sebelum test besar

---

**Happy Load Testing! 🚀**
