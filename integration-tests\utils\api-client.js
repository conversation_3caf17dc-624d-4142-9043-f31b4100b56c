const axios = require('axios');

class ApiClient {
  constructor(baseURL, defaultHeaders = {}, silent = false) {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        ...defaultHeaders
      }
    });

    // Only add logging interceptors if not in silent mode
    if (!silent) {
      // Add request interceptor for logging
      this.client.interceptors.request.use(
        (config) => {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
          if (config.data) {
            console.log('📤 Request data:', JSON.stringify(config.data, null, 2));
          }
          return config;
        },
        (error) => {
          console.error('❌ Request error:', error.message);
          return Promise.reject(error);
        }
      );

      // Add response interceptor for logging
      this.client.interceptors.response.use(
        (response) => {
          console.log(`✅ ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
          console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
          return response;
        },
        (error) => {
          if (error.response) {
            console.error(`❌ ${error.response.status} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);
            console.error('📥 Error response:', JSON.stringify(error.response.data, null, 2));
          } else {
            console.error('❌ Network error:', error.message);
          }
          return Promise.reject(error);
        }
      );
    }
  }

  setAuthToken(token) {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  removeAuthToken() {
    delete this.client.defaults.headers.common['Authorization'];
  }

  setServiceAuth(apiKey) {
    this.client.defaults.headers.common['X-Service-API-Key'] = apiKey;
  }

  async get(url, config = {}) {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post(url, data = {}, config = {}) {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put(url, data = {}, config = {}) {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete(url, config = {}) {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  async patch(url, data = {}, config = {}) {
    const response = await this.client.patch(url, data, config);
    return response.data;
  }
}

module.exports = ApiClient;
