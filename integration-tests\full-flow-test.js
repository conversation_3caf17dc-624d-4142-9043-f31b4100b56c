#!/usr/bin/env node

/**
 * ATMA Full Flow Integration Test
 * Standalone script to test the complete flow:
 * 1. Register user
 * 2. Login user
 * 3. Submit assessment
 * 4. Wait for processing
 * 5. Verify results in archive service
 */

require('dotenv').config();
const ApiClient = require('./utils/api-client');
const fs = require('fs');
const path = require('path');
const io = require('socket.io-client');

// Configuration
const config = {
  authServiceUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  assessmentServiceUrl: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
  archiveServiceUrl: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  notificationServiceUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
  testEmail: `test-${Date.now()}-${Math.random().toString(36).substring(7)}@example.com`,
  testPassword: process.env.TEST_PASSWORD || 'password123',
  maxWaitTime: parseInt(process.env.WAIT_FOR_PROCESSING) || 120000
};

// API Clients (silent mode - no request/response logging)
const authClient = new ApiClient(config.authServiceUrl, {}, true);
const assessmentClient = new ApiClient(config.assessmentServiceUrl, {}, true);
const archiveClient = new ApiClient(config.archiveServiceUrl, {}, true);

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const loadSampleAssessment = () => {
  const filePath = path.join(__dirname, 'test-data', 'sample-assessment.json');
  const data = fs.readFileSync(filePath, 'utf8');
  return JSON.parse(data);
};

const logStep = (step, message, startTime) => {
  console.log(`\n🔸 Step ${step}: ${message}`);
  return Date.now();
};

const logStepComplete = (step, message, startTime) => {
  const duration = Date.now() - startTime;
  console.log(`✅ Step ${step}: ${message} (${duration}ms)`);
  return duration;
};

const logError = (message, error) => {
  console.error(`❌ ${message}`);
  if (error.response) {
    console.error(`   Status: ${error.response.status}`);
    console.error(`   Data:`, error.response.data);
  } else {
    console.error(`   Error:`, error.message);
  }
};

// WebSocket helper functions
const connectToNotifications = (token) => {
  return new Promise((resolve, reject) => {
    const socket = io(config.notificationServiceUrl, {
      transports: ['websocket'],
      timeout: 5000
    });

    socket.on('connect', () => {
      socket.emit('authenticate', { token });
    });

    socket.on('authenticated', (data) => {
      if (data.success) {
        resolve(socket);
      } else {
        reject(new Error('Authentication failed'));
      }
    });

    socket.on('auth_error', (error) => {
      reject(new Error(error.message));
    });

    socket.on('connect_error', (error) => {
      reject(new Error(`Connection failed: ${error.message}`));
    });
  });
};

const waitForAnalysisComplete = (socket, jobId, maxWaitTime) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Analysis timeout after ${maxWaitTime / 1000} seconds`));
    }, maxWaitTime);

    socket.on('analysis-complete', (data) => {
      if (data.jobId === jobId) {
        clearTimeout(timeout);
        resolve(data);
      }
    });

    socket.on('analysis-failed', (data) => {
      if (data.jobId === jobId) {
        clearTimeout(timeout);
        reject(new Error(`Analysis failed: ${data.error || 'Unknown error'}`));
      }
    });
  });
};

// Main test function
async function runFullFlowTest() {
  console.log('🚀 ATMA Full Flow Integration Test');
  console.log(`📧 Email: ${config.testEmail}`);

  const testStartTime = Date.now();
  const stepTimes = {};
  let testUser, authToken, assessmentJobId, analysisResult, socket;

  try {
    // Step 1: Register User
    const step1Start = logStep(1, 'Register User');
    const registerResponse = await authClient.post('/auth/register', {
      email: config.testEmail,
      password: config.testPassword
    });

    if (!registerResponse.success) {
      throw new Error('Registration failed');
    }

    testUser = registerResponse.data.user;
    stepTimes.register = logStepComplete(1, 'User registered', step1Start);

    // Step 2: Login User
    const step2Start = logStep(2, 'Login User');
    const loginResponse = await authClient.post('/auth/login', {
      email: config.testEmail,
      password: config.testPassword
    });

    if (!loginResponse.success) {
      throw new Error('Login failed');
    }

    authToken = loginResponse.data.token;
    stepTimes.login = logStepComplete(2, 'Login successful', step2Start);

    // Step 3: Connect to WebSocket
    const step3Start = logStep(3, 'Connect to notifications');
    socket = await connectToNotifications(authToken);
    stepTimes.websocket = logStepComplete(3, 'WebSocket connected', step3Start);

    // Step 4: Submit Assessment
    const step4Start = logStep(4, 'Submit Assessment');
    const assessmentData = loadSampleAssessment();
    assessmentClient.setAuthToken(authToken);

    const submitResponse = await assessmentClient.post('/assessments/submit', assessmentData);

    if (!submitResponse.success) {
      throw new Error('Assessment submission failed');
    }

    assessmentJobId = submitResponse.data.jobId;
    stepTimes.submit = logStepComplete(4, `Assessment submitted (Job: ${assessmentJobId})`, step4Start);

    // Step 5: Wait for Processing via WebSocket
    const step5Start = logStep(5, 'Wait for analysis completion');
    const analysisResult = await waitForAnalysisComplete(socket, assessmentJobId, config.maxWaitTime);
    stepTimes.analysis = logStepComplete(5, 'Analysis completed', step5Start);

    // Step 6: Verify Results in Archive
    const step6Start = logStep(6, 'Verify results in archive');

    // Wait a bit for the result to be saved to archive
    await sleep(2000);

    archiveClient.setAuthToken(authToken);
    const archiveResponse = await archiveClient.get('/archive/results');

    if (!archiveResponse.success) {
      throw new Error('Failed to retrieve results from archive service');
    }

    const results = archiveResponse.data.results;
    const archiveResult = results.find(result => result.user_id === testUser.id);

    if (!archiveResult) {
      throw new Error('Analysis result not found in archive service');
    }

    stepTimes.archive = logStepComplete(6, `Result verified (ID: ${archiveResult.id})`, step6Start);

    // Step 7: Verify Data Integrity
    const step7Start = logStep(7, 'Verify data integrity');

    // Verify persona profile
    if (!archiveResult.persona_profile) {
      throw new Error('Persona profile missing from archive');
    }

    const personaProfile = archiveResult.persona_profile;
    const requiredFields = ['archetype', 'shortSummary', 'strengths', 'weaknesses', 'careerRecommendation', 'insights'];

    for (const field of requiredFields) {
      if (!personaProfile[field]) {
        throw new Error(`Persona profile missing field: ${field}`);
      }
    }

    stepTimes.validation = logStepComplete(7, `Data integrity verified (${personaProfile.archetype})`, step7Start);

    // Cleanup
    if (socket) {
      socket.disconnect();
    }

    // Final Summary
    const totalTime = Date.now() - testStartTime;
    console.log('\n🎉 FULL FLOW TEST COMPLETED SUCCESSFULLY!');
    console.log(`📊 Summary: ${config.testEmail.split('@')[0]} → ${personaProfile.archetype}`);
    console.log(`⏱️ Total: ${totalTime}ms | Steps: Register(${stepTimes.register}ms) Login(${stepTimes.login}ms) WebSocket(${stepTimes.websocket}ms) Submit(${stepTimes.submit}ms) Analysis(${stepTimes.analysis}ms) Archive(${stepTimes.archive}ms) Validation(${stepTimes.validation}ms)`);

    process.exit(0);

  } catch (error) {
    if (socket) {
      socket.disconnect();
    }
    logError('Test failed', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runFullFlowTest();
}

module.exports = { runFullFlowTest };
