'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add error_message column
    await queryInterface.addColumn(
      {
        tableName: 'analysis_results',
        schema: 'archive'
      },
      'error_message',
      {
        type: Sequelize.TEXT,
        allowNull: true
      }
    );

    // Change persona_profile to allow null
    await queryInterface.changeColumn(
      {
        tableName: 'analysis_results',
        schema: 'archive'
      },
      'persona_profile',
      {
        type: Sequelize.JSONB,
        allowNull: true
      }
    );
  },

  async down(queryInterface, Sequelize) {
    // Remove error_message column
    await queryInterface.removeColumn(
      {
        tableName: 'analysis_results',
        schema: 'archive'
      },
      'error_message'
    );

    // Change persona_profile back to not null
    await queryInterface.changeColumn(
      {
        tableName: 'analysis_results',
        schema: 'archive'
      },
      'persona_profile',
      {
        type: Sequelize.JSONB,
        allowNull: false
      }
    );
  }
};
